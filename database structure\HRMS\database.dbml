// IT Company Management System Database Schema
// Comprehensive HRMS with Project Management, CRM, Finance, and Operations

Project IT_Company_Management {
  database_type: 'PostgreSQL'
  Note: 'Complete IT Company Management System with HRMS, CRM, Project Management, Finance, and Operations modules'
}

// ===================================
// 1. AUTHENTICATION & USER MANAGEMENT
// ===================================

Table users {
  id uuid [primary key, default: `gen_random_uuid()`]
  username varchar(50) [unique, not null]
  email varchar(255) [unique, not null]
  password_hash varchar(255) [not null]
  first_name varchar(100) [not null]
  last_name varchar(100) [not null]
  phone varchar(20)
  profile_picture varchar(500)
  is_active boolean [default: true]
  is_verified boolean [default: false]
  last_login timestamp
  failed_login_attempts integer [default: 0]
  locked_until timestamp
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  Note: 'Core user authentication and profile data'
}

Table roles {
  id uuid [primary key, default: `gen_random_uuid()`]
  name varchar(50) [unique, not null]
  description text
  is_active boolean [default: true]
  created_at timestamp [default: `now()`]

  Note: 'System roles: Admin, HR, Project Manager, Developer, Finance, Client, etc.'
}

Table permissions {
  id uuid [primary key, default: `gen_random_uuid()`]
  name varchar(100) [unique, not null]
  description text
  module varchar(50) [not null]
  action varchar(50) [not null]
  created_at timestamp [default: `now()`]

  Note: 'Granular permissions for different modules and actions'
}

Table user_roles {
  id uuid [primary key, default: `gen_random_uuid()`]
  user_id uuid [not null, ref: > users.id]
  role_id uuid [not null, ref: > roles.id]
  assigned_by uuid [ref: > users.id]
  assigned_at timestamp [default: `now()`]
  expires_at timestamp
  is_active boolean [default: true]

  Note: 'Many-to-many relationship between users and roles'
}

Table role_permissions {
  id uuid [primary key, default: `gen_random_uuid()`]
  role_id uuid [not null, ref: > roles.id]
  permission_id uuid [not null, ref: > permissions.id]
  created_at timestamp [default: `now()`]

  Note: 'Permissions assigned to roles'
}

Table user_sessions {
  id uuid [primary key, default: `gen_random_uuid()`]
  user_id uuid [not null, ref: > users.id]
  session_token varchar(255) [unique, not null]
  ip_address inet
  user_agent text
  is_active boolean [default: true]
  expires_at timestamp [not null]
  created_at timestamp [default: `now()`]

  Note: 'Active user sessions for security tracking'
}

Table activity_logs {
  id uuid [primary key, default: `gen_random_uuid()`]
  user_id uuid [ref: > users.id]
  action varchar(100) [not null]
  module varchar(50) [not null]
  resource_type varchar(50)
  resource_id uuid
  ip_address inet
  user_agent text
  details jsonb
  created_at timestamp [default: `now()`]

  Note: 'Comprehensive activity logging for audit trails'
}

// ===================================
// 2. EMPLOYEE MANAGEMENT
// ===================================

Table departments {
  id uuid [primary key, default: `gen_random_uuid()`]
  name varchar(100) [unique, not null]
  description text
  head_id uuid [ref: > employees.id]
  parent_department_id uuid [ref: > departments.id]
  budget decimal(15,2)
  is_active boolean [default: true]
  created_at timestamp [default: `now()`]
  updated_at timestamp [default: `now()`]

  Note: 'Organizational departments with hierarchy support'
}

Table designations {
  id uuid [primary key, default: `gen_random_uuid()`]
  title varchar(100) [unique, not null]
  description text
  level integer [not null]
  department_id uuid [ref: > departments.id]
  min_salary decimal(12,2)
  max_salary decimal(12,2)
  is_active boolean [default: true]
  created_at timestamp [default: `now()`]

  Note: 'Job titles and positions within the company'
}